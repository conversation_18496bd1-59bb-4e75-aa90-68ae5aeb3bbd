# IDA Pro Multi-layer Password Hunter Script - Fixed Version
# Avoids infinite loops and provides better error handling

import ida_bytes
import ida_name
import ida_search
import ida_funcs
import ida_xref
import idc
import idaapi
import idautils

class MultiLayerPasswordHunter:
    def __init__(self):
        self.found_passwords = ["Sultan"]
        self.potential_passwords = []
        self.decryption_functions = []
        self.processed_addresses = set()
        
    def hunt_layer2_passwords(self):
        print("=== Starting Multi-Layer Password Search ===")
        
        try:
            self.search_around_known_password()
            self.search_in_decrypt_functions()
            self.search_password_generation()
            self.search_config_passwords()
        except Exception as e:
            print("Error during search: %s" % str(e))
    
    def search_around_known_password(self):
        print("1. Searching around 'Sultan' password...")
        
        sultan_addr = ida_search.find_text(0, 0, 0, "Sultan", ida_search.SEARCH_DOWN)
        found_count = 0
        
        while sultan_addr != idaapi.BADADDR and found_count < 20:
            if sultan_addr not in self.processed_addresses:
                print("Found 'Sultan' at address: 0x%X" % sultan_addr)
                self.processed_addresses.add(sultan_addr)
                found_count += 1
                
                try:
                    self.analyze_surrounding_code(sultan_addr)
                except Exception as e:
                    print("Error analyzing address 0x%X: %s" % (sultan_addr, str(e)))
            
            # Move past the current match to avoid infinite loop
            next_addr = sultan_addr + 6  # Length of "Sultan"
            if next_addr <= sultan_addr:  # Safety check
                break
                
            sultan_addr = ida_search.find_text(next_addr, 0, 0, "Sultan", ida_search.SEARCH_DOWN)
        
        print("Found %d instances of 'Sultan'" % found_count)
    
    def analyze_surrounding_code(self, addr):
        print("  Analyzing code around address: 0x%X" % addr)
        
        func = ida_funcs.get_func(addr)
        if func:
            func_name = ida_name.get_name(func.start_ea)
            print("  Function: %s at 0x%X" % (func_name, func.start_ea))
            
            # Search for strings in this function
            self.search_strings_in_function(func)
            
            # Look for nearby strings (within 256 bytes)
            self.search_nearby_strings(addr, 256)
        else:
            print("  No function found at this address")
            # Still search for nearby strings
            self.search_nearby_strings(addr, 128)
    
    def search_strings_in_function(self, func):
        if func.start_ea in self.processed_addresses:
            return
        self.processed_addresses.add(func.start_ea)
        
        current_addr = func.start_ea
        string_count = 0
        
        while current_addr < func.end_ea and string_count < 50:
            try:
                if ida_bytes.is_strlit(ida_bytes.get_flags(current_addr)):
                    str_content = ida_bytes.get_strlit_contents(current_addr, -1, ida_bytes.STRTYPE_C)
                    if str_content and len(str_content) > 3:
                        try:
                            decoded_str = str_content.decode('utf-8', errors='ignore')
                            if decoded_str and len(decoded_str.strip()) > 0:
                                print("    String: '%s'" % decoded_str[:50])
                                string_count += 1
                                
                                if self.is_potential_password(decoded_str):
                                    self.potential_passwords.append({
                                        'password': decoded_str,
                                        'address': current_addr,
                                        'context': 'function_string'
                                    })
                                    print("    *** POTENTIAL PASSWORD: '%s' ***" % decoded_str)
                        except:
                            pass
                
                current_addr = ida_bytes.next_head(current_addr, func.end_ea)
                if current_addr == idaapi.BADADDR:
                    break
                    
            except Exception as e:
                print("    Error processing address 0x%X: %s" % (current_addr, str(e)))
                break
    
    def search_nearby_strings(self, center_addr, radius):
        start_addr = max(0, center_addr - radius)
        end_addr = center_addr + radius
        
        current_addr = start_addr
        while current_addr < end_addr:
            try:
                if ida_bytes.is_strlit(ida_bytes.get_flags(current_addr)):
                    str_content = ida_bytes.get_strlit_contents(current_addr, -1, ida_bytes.STRTYPE_C)
                    if str_content and len(str_content) > 3:
                        try:
                            decoded_str = str_content.decode('utf-8', errors='ignore')
                            if decoded_str and len(decoded_str.strip()) > 0:
                                distance = abs(current_addr - center_addr)
                                print("    Nearby string (distance %d): '%s'" % (distance, decoded_str[:30]))
                                
                                if self.is_potential_password(decoded_str):
                                    self.potential_passwords.append({
                                        'password': decoded_str,
                                        'address': current_addr,
                                        'context': 'nearby_string'
                                    })
                                    print("    *** POTENTIAL PASSWORD NEARBY: '%s' ***" % decoded_str)
                        except:
                            pass
                
                current_addr = ida_bytes.next_head(current_addr, end_addr)
                if current_addr == idaapi.BADADDR:
                    break
                    
            except Exception as e:
                break
    
    def search_in_decrypt_functions(self):
        print("2. Searching for decryption functions...")
        
        decrypt_patterns = [
            "decrypt", "Decrypt", "DECRYPT",
            "decode", "Decode", "DECODE", 
            "unlock", "Unlock", "UNLOCK",
            "crypt", "Crypt", "CRYPT",
            "cipher", "Cipher", "CIPHER"
        ]
        
        for pattern in decrypt_patterns:
            try:
                addr = ida_search.find_text(0, 0, 0, pattern, ida_search.SEARCH_DOWN)
                found_count = 0
                
                while addr != idaapi.BADADDR and found_count < 10:
                    func = ida_funcs.get_func(addr)
                    if func and func.start_ea not in [f['addr'] for f in self.decryption_functions]:
                        func_name = ida_name.get_name(func.start_ea)
                        print("  Found decrypt function: %s at 0x%X" % (func_name, func.start_ea))
                        
                        self.decryption_functions.append({
                            'name': func_name,
                            'addr': func.start_ea,
                            'pattern': pattern
                        })
                        found_count += 1
                    
                    next_addr = addr + len(pattern)
                    if next_addr <= addr:
                        break
                    addr = ida_search.find_text(next_addr, 0, 0, pattern, ida_search.SEARCH_DOWN)
                    
            except Exception as e:
                print("  Error searching for pattern '%s': %s" % (pattern, str(e)))
    
    def search_password_generation(self):
        print("3. Searching for password generation functions...")
        
        generation_patterns = [
            "generatePassword", "GeneratePassword",
            "createPassword", "CreatePassword",
            "buildPassword", "BuildPassword",
            "makePassword", "MakePassword",
            "generateKey", "GenerateKey",
            "createKey", "CreateKey"
        ]
        
        for pattern in generation_patterns:
            try:
                addr = ida_search.find_text(0, 0, 0, pattern, ida_search.SEARCH_DOWN)
                if addr != idaapi.BADADDR:
                    print("  Found password generation: %s at 0x%X" % (pattern, addr))
                    func = ida_funcs.get_func(addr)
                    if func:
                        print("  *** SUGGESTION: Set breakpoint at 0x%X ***" % func.start_ea)
            except Exception as e:
                print("  Error searching for pattern '%s': %s" % (pattern, str(e)))
    
    def search_config_passwords(self):
        print("4. Searching for config passwords...")
        
        config_patterns = ["config", "Config", "setting", "Setting", "option", "Option"]
        
        for pattern in config_patterns:
            try:
                addr = ida_search.find_text(0, 0, 0, pattern, ida_search.SEARCH_DOWN)
                found_count = 0
                
                while addr != idaapi.BADADDR and found_count < 5:
                    print("  Found config related: %s at 0x%X" % (pattern, addr))
                    self.search_nearby_strings(addr, 64)
                    found_count += 1
                    
                    next_addr = addr + len(pattern)
                    if next_addr <= addr:
                        break
                    addr = ida_search.find_text(next_addr, 0, 0, pattern, ida_search.SEARCH_DOWN)
                    
            except Exception as e:
                print("  Error searching for pattern '%s': %s" % (pattern, str(e)))
    
    def is_potential_password(self, text):
        if not text or len(text) < 4 or len(text) > 50:
            return False
        
        text = text.strip()
        if not text:
            return False
        
        exclude_patterns = [
            'function', 'class', 'method', 'return', 'void',
            'string', 'int', 'bool', 'float', 'double',
            'public', 'private', 'static', 'const', 'null',
            'true', 'false', 'this', 'that', 'with', 'from'
        ]
        
        text_lower = text.lower()
        if any(pattern in text_lower for pattern in exclude_patterns):
            return False
        
        # High priority keywords
        password_keywords = [
            'pass', 'key', 'secret', 'token', 'auth',
            'crypt', 'hash', 'salt', 'seed', 'sultan',
            'password', 'unlock', 'decode', 'decrypt'
        ]
        
        if any(keyword in text_lower for keyword in password_keywords):
            return True
        
        # Check character composition
        has_alpha = any(c.isalpha() for c in text)
        has_digit = any(c.isdigit() for c in text)
        has_special = any(c in '!@#$%^&*()_+-=[]{}|;:,.<>?' for c in text)
        
        # Potential password if it has mixed character types or is reasonably long
        return has_alpha and (has_digit or has_special or len(text) >= 8)
    
    def generate_report(self):
        print("\n" + "="*60)
        print("MULTI-LAYER PASSWORD SEARCH REPORT")
        print("="*60)
        
        print("\nKnown passwords: %d" % len(self.found_passwords))
        for pwd in self.found_passwords:
            print("   * %s" % pwd)
        
        print("\nPotential passwords found: %d" % len(self.potential_passwords))
        if self.potential_passwords:
            for i, pwd_info in enumerate(self.potential_passwords[:20]):  # Limit output
                print("   %d. '%s' at 0x%X (%s)" % (i+1, pwd_info['password'], pwd_info['address'], pwd_info['context']))
            
            if len(self.potential_passwords) > 20:
                print("   ... and %d more" % (len(self.potential_passwords) - 20))
        
        print("\nDecryption functions found: %d" % len(self.decryption_functions))
        for func_info in self.decryption_functions:
            print("   * %s at 0x%X" % (func_info['name'], func_info['addr']))
        
        print("\n" + "="*60)
        print("SUMMARY AND NEXT STEPS")
        print("="*60)
        
        if self.potential_passwords:
            print("*** IMPORTANT: Found %d potential passwords! ***" % len(self.potential_passwords))
            print("Top candidates to try:")
            for i, pwd_info in enumerate(self.potential_passwords[:5]):
                print("   %d. '%s'" % (i+1, pwd_info['password']))
        
        if self.decryption_functions:
            print("\nSuggested breakpoints for dynamic analysis:")
            for func_info in self.decryption_functions[:5]:
                print("   * 0x%X (%s)" % (func_info['addr'], func_info['name']))
        
        print("\nRecommended next steps:")
        print("   1. Test the potential passwords found above")
        print("   2. Set breakpoints at decryption functions")
        print("   3. Use Frida to hook password verification")
        print("   4. Try Cpp2IL for static analysis")

# Execute the search
print("Starting Sultan's Game multi-layer password search...")
print("This may take a few minutes...")

try:
    hunter = MultiLayerPasswordHunter()
    hunter.hunt_layer2_passwords()
    hunter.generate_report()
    print("\nSearch completed successfully!")
except Exception as e:
    print("Error during execution: %s" % str(e))
    print("Please check IDA Pro console for more details.")
