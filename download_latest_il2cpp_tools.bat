@echo off
echo ========================================
echo 下载最新版本的IL2CPP分析工具
echo ========================================
echo.

echo 🔍 当前Il2CppInspector版本可能过旧，建议下载最新版本
echo.

echo 💡 推荐下载以下工具:
echo.

echo 1. Il2CppInspector (最新版本)
echo    下载地址: https://github.com/djkaty/Il2CppInspector/releases/latest
echo    建议下载: Il2CppInspector-win-x64.zip
echo.

echo 2. Cpp2IL (备选方案)
echo    下载地址: https://github.com/SamboyCoding/Cpp2IL/releases/latest
echo    建议下载: Cpp2IL-Win.zip
echo.

echo 3. Il2CppDumper (最新版本)
echo    下载地址: https://github.com/Perfare/Il2CppDumper/releases/latest
echo    建议下载: Il2CppDumper-v6.7.46.zip
echo.

set /p choice="选择要打开的下载页面 (1/2/3/all): "

if "%choice%"=="1" (
    start https://github.com/djkaty/Il2CppInspector/releases/latest
) else if "%choice%"=="2" (
    start https://github.com/SamboyCoding/Cpp2IL/releases/latest
) else if "%choice%"=="3" (
    start https://github.com/Perfare/Il2CppDumper/releases/latest
) else if "%choice%"=="all" (
    start https://github.com/djkaty/Il2CppInspector/releases/latest
    timeout /t 2 >nul
    start https://github.com/SamboyCoding/Cpp2IL/releases/latest
    timeout /t 2 >nul
    start https://github.com/Perfare/Il2CppDumper/releases/latest
)

echo.
echo 📋 下载完成后的使用说明:
echo ================================
echo.
echo Il2CppInspector 使用方法:
echo 1. 解压下载的文件
echo 2. 运行 Il2CppInspector.exe
echo 3. 拖拽 global-metadata.dat 和 GameAssembly.dll
echo.
echo Cpp2IL 使用方法:
echo 1. 解压下载的文件
echo 2. 运行命令: Cpp2IL.exe --game-path="D:\stemd\steamapps\common\Sultan's Game"
echo.
echo Il2CppDumper 使用方法:
echo 1. 解压下载的文件
echo 2. 运行 Il2CppDumper.exe
echo 3. 选择 GameAssembly.dll 和 global-metadata.dat
echo.

pause
