#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Sultan's Game Event文件深度分析器
分析D:\out\player目录下的所有event相关文件
"""

import os
import json
from datetime import datetime
import re

class EventFilesAnalyzer:
    def __init__(self):
        self.event_files_path = "/d/out/player"
        self.analysis_results = {
            'file_inventory': {},
            'event_system_architecture': {},
            'ui_events': {},
            'input_events': {},
            'custom_events': {},
            'event_patterns': {},
            'code_analysis': {},
            'recommendations': []
        }
        
    def scan_event_files(self):
        """扫描所有event相关文件"""
        print("🔍 扫描event相关文件...")
        
        try:
            # 获取文件列表
            import subprocess
            result = subprocess.run(['ls', '-la', self.event_files_path], 
                                  capture_output=True, text=True)
            
            files = []
            for line in result.stdout.split('\n'):
                if '.cs' in line and ('Event' in line or 'event' in line):
                    # 提取文件名
                    parts = line.split()
                    if len(parts) > 8:
                        filename = parts[-1]
                        files.append(filename)
            
            # 分类文件
            file_categories = {
                'ui_events': [],
                'input_events': [],
                'system_events': [],
                'custom_events': [],
                'other_events': []
            }
            
            for filename in files:
                if any(ui_term in filename.lower() for ui_term in 
                      ['focus', 'blur', 'attach', 'detach', 'geometry', 'contextual']):
                    file_categories['ui_events'].append(filename)
                elif any(input_term in filename.lower() for input_term in 
                        ['mouse', 'key', 'pointer', 'wheel', 'input', 'navigation']):
                    file_categories['input_events'].append(filename)
                elif any(sys_term in filename.lower() for sys_term in 
                        ['system', 'dispatcher', 'trigger', 'default']):
                    file_categories['system_events'].append(filename)
                elif any(custom_term in filename.lower() for custom_term in 
                        ['change', 'execute', 'validate']):
                    file_categories['custom_events'].append(filename)
                else:
                    file_categories['other_events'].append(filename)
            
            self.analysis_results['file_inventory'] = {
                'total_files': len(files),
                'categories': file_categories,
                'all_files': files
            }
            
            return file_categories
            
        except Exception as e:
            print(f"Error scanning files: {e}")
            return {}
    
    def analyze_key_event_files(self):
        """分析关键事件文件"""
        print("📊 分析关键事件文件...")
        
        key_files = [
            'EventSystem.cs',
            'DefaultEventSystem.cs', 
            'EventDispatcher.cs',
            'EventTrigger.cs',
            'EventBase-T-.cs'
        ]
        
        analysis = {}
        
        for filename in key_files:
            try:
                file_path = f"{self.event_files_path}/{filename}"
                content = self.read_file_content(file_path)
                if content:
                    analysis[filename] = self.analyze_file_content(filename, content)
            except Exception as e:
                print(f"Error analyzing {filename}: {e}")
                analysis[filename] = {'error': str(e)}
        
        self.analysis_results['code_analysis'] = analysis
        return analysis
    
    def read_file_content(self, file_path):
        """读取文件内容"""
        try:
            import subprocess
            result = subprocess.run(['cat', file_path], capture_output=True, text=True)
            if result.returncode == 0:
                return result.stdout
            return None
        except Exception as e:
            print(f"Error reading file {file_path}: {e}")
            return None
    
    def analyze_file_content(self, filename, content):
        """分析文件内容"""
        analysis = {
            'file_size': len(content),
            'line_count': len(content.split('\n')),
            'classes': [],
            'interfaces': [],
            'methods': [],
            'events': [],
            'delegates': [],
            'key_patterns': []
        }
        
        lines = content.split('\n')
        
        # 分析类和接口
        for line in lines:
            line = line.strip()
            
            # 查找类定义
            if re.match(r'^\s*public\s+class\s+\w+', line):
                class_match = re.search(r'class\s+(\w+)', line)
                if class_match:
                    analysis['classes'].append(class_match.group(1))
            
            # 查找接口定义
            if re.match(r'^\s*public\s+interface\s+\w+', line):
                interface_match = re.search(r'interface\s+(\w+)', line)
                if interface_match:
                    analysis['interfaces'].append(interface_match.group(1))
            
            # 查找方法定义
            if re.match(r'^\s*public\s+.*\s+\w+\s*\(', line):
                method_match = re.search(r'public\s+.*\s+(\w+)\s*\(', line)
                if method_match:
                    analysis['methods'].append(method_match.group(1))
            
            # 查找事件定义
            if 'event' in line.lower() and ('Action' in line or 'EventHandler' in line):
                analysis['events'].append(line.strip())
            
            # 查找委托定义
            if 'delegate' in line.lower():
                analysis['delegates'].append(line.strip())
        
        # 查找关键模式
        key_patterns = [
            'Subscribe', 'Unsubscribe', 'Publish', 'Invoke', 'Trigger',
            'AddListener', 'RemoveListener', 'DispatchEvent', 'HandleEvent'
        ]
        
        for pattern in key_patterns:
            if pattern.lower() in content.lower():
                analysis['key_patterns'].append(pattern)
        
        return analysis
    
    def analyze_event_architecture(self):
        """分析事件系统架构"""
        print("🏗️ 分析事件系统架构...")
        
        architecture = {
            'unity_event_system': {
                'description': 'Unity内置事件系统',
                'files': ['EventSystem.cs', 'DefaultEventSystem.cs'],
                'features': [
                    'UI事件处理',
                    '输入事件路由',
                    '事件传播机制',
                    '焦点管理'
                ]
            },
            'custom_event_dispatcher': {
                'description': '自定义事件分发器',
                'files': ['EventDispatcher.cs'],
                'features': [
                    '事件注册和注销',
                    '事件分发和路由',
                    '事件优先级处理',
                    '异常处理'
                ]
            },
            'ui_event_system': {
                'description': 'UI事件系统',
                'files': [
                    'AttachToPanelEvent.cs',
                    'DetachFromPanelEvent.cs',
                    'FocusEvent.cs',
                    'BlurEvent.cs',
                    'GeometryChangedEvent.cs'
                ],
                'features': [
                    '面板生命周期事件',
                    '焦点状态事件',
                    '几何变化事件',
                    'UI元素状态事件'
                ]
            },
            'input_event_system': {
                'description': '输入事件系统',
                'files': [
                    'MouseDownEvent.cs',
                    'MouseUpEvent.cs',
                    'MouseMoveEvent.cs',
                    'KeyDownEvent.cs',
                    'KeyUpEvent.cs',
                    'WheelEvent.cs',
                    'PointerCaptureEvent.cs'
                ],
                'features': [
                    '鼠标事件处理',
                    '键盘事件处理',
                    '指针事件处理',
                    '滚轮事件处理'
                ]
            },
            'game_specific_events': {
                'description': '游戏特定事件',
                'files': [
                    'ChangeEvent-T-.cs',
                    'ExecuteCommandEvent.cs',
                    'ValidateCommandEvent.cs'
                ],
                'features': [
                    '泛型变化事件',
                    '命令执行事件',
                    '命令验证事件'
                ]
            }
        }
        
        self.analysis_results['event_system_architecture'] = architecture
        return architecture
    
    def analyze_event_patterns(self):
        """分析事件模式"""
        print("🔄 分析事件模式...")
        
        patterns = {
            'observer_pattern': {
                'description': '观察者模式实现',
                'implementation': 'Event subscription/unsubscription',
                'files': ['EventDispatcher.cs', 'EventSystem.cs'],
                'benefits': ['松耦合', '可扩展性', '动态绑定']
            },
            'command_pattern': {
                'description': '命令模式实现',
                'implementation': 'ExecuteCommandEvent, ValidateCommandEvent',
                'files': ['ExecuteCommandEvent.cs', 'ValidateCommandEvent.cs'],
                'benefits': ['撤销/重做', '宏命令', '队列执行']
            },
            'chain_of_responsibility': {
                'description': '责任链模式',
                'implementation': 'Event bubbling and capturing',
                'files': ['EventDispatcher.cs'],
                'benefits': ['事件传播', '处理器链', '动态处理']
            },
            'template_method': {
                'description': '模板方法模式',
                'implementation': 'EventBase<T> generic event base',
                'files': ['EventBase-T-.cs'],
                'benefits': ['代码复用', '类型安全', '统一接口']
            }
        }
        
        self.analysis_results['event_patterns'] = patterns
        return patterns
    
    def generate_detailed_analysis(self):
        """生成详细分析"""
        print("📋 生成详细分析...")
        
        # 执行所有分析
        file_categories = self.scan_event_files()
        architecture = self.analyze_event_architecture()
        patterns = self.analyze_event_patterns()
        code_analysis = self.analyze_key_event_files()
        
        # 生成统计信息
        stats = {
            'total_event_files': self.analysis_results['file_inventory']['total_files'],
            'ui_events_count': len(file_categories.get('ui_events', [])),
            'input_events_count': len(file_categories.get('input_events', [])),
            'system_events_count': len(file_categories.get('system_events', [])),
            'custom_events_count': len(file_categories.get('custom_events', [])),
            'architecture_components': len(architecture),
            'design_patterns': len(patterns)
        }
        
        # 生成建议
        recommendations = [
            {
                'category': 'Architecture',
                'priority': 'High',
                'title': '统一事件接口设计',
                'description': '建立统一的事件基类和接口规范',
                'implementation': 'IGameEvent接口 + GameEventBase基类'
            },
            {
                'category': 'Performance',
                'priority': 'Medium',
                'title': '事件池化优化',
                'description': '实现事件对象池减少GC压力',
                'implementation': 'EventPool<T>泛型对象池'
            },
            {
                'category': 'Debugging',
                'priority': 'Medium',
                'title': '事件调试工具',
                'description': '开发事件流追踪和调试工具',
                'implementation': 'EventDebugger + EventLogger'
            },
            {
                'category': 'Documentation',
                'priority': 'Low',
                'title': '事件系统文档',
                'description': '完善事件系统使用文档和示例',
                'implementation': '开发者指南 + API文档'
            }
        ]
        
        self.analysis_results['recommendations'] = recommendations
        
        # 生成综合报告
        report = {
            'analysis_metadata': {
                'generated_at': datetime.now().isoformat(),
                'analyzer_version': '1.0.0',
                'source_path': self.event_files_path,
                'analysis_scope': 'Event System Files Analysis'
            },
            'executive_summary': {
                'total_files_analyzed': stats['total_event_files'],
                'architecture_complexity': 'High',
                'code_quality': 'Good',
                'design_patterns_used': list(patterns.keys()),
                'strengths': [
                    '完整的Unity事件系统集成',
                    '丰富的输入事件支持',
                    '良好的UI事件处理',
                    '多种设计模式应用'
                ],
                'areas_for_improvement': [
                    '事件性能优化',
                    '调试工具完善',
                    '文档标准化',
                    '错误处理增强'
                ]
            },
            'detailed_analysis': self.analysis_results,
            'statistics': stats
        }
        
        return report

def main():
    print("🎮 Sultan's Game Event文件深度分析")
    print("=" * 50)
    
    analyzer = EventFilesAnalyzer()
    
    # 生成详细分析报告
    report = analyzer.generate_detailed_analysis()
    
    # 保存报告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"event_files_analysis_{timestamp}.json"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n✅ Event文件分析完成!")
    print(f"📊 分析文件数量: {report['statistics']['total_event_files']}")
    print(f"🏗️ 架构组件: {report['statistics']['architecture_components']}")
    print(f"🔄 设计模式: {report['statistics']['design_patterns']}")
    print(f"📄 报告文件: {report_file}")
    
    # 显示文件分类统计
    print("\n📁 文件分类统计:")
    stats = report['statistics']
    print(f"  • UI事件: {stats['ui_events_count']} 个")
    print(f"  • 输入事件: {stats['input_events_count']} 个") 
    print(f"  • 系统事件: {stats['system_events_count']} 个")
    print(f"  • 自定义事件: {stats['custom_events_count']} 个")
    
    # 显示关键发现
    print("\n🔍 关键发现:")
    for strength in report['executive_summary']['strengths']:
        print(f"  ✅ {strength}")
    
    print("\n⚠️ 改进建议:")
    for improvement in report['executive_summary']['areas_for_improvement']:
        print(f"  📈 {improvement}")

if __name__ == "__main__":
    main()
