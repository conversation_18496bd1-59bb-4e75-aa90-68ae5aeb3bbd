# Sultan's Game UI系统可视化图表

## 1. UI系统架构图
```mermaid

graph TB
    subgraph "Canvas层次结构"
        MC[Main Canvas<br/>Sort Order: 0]
        PC[Popup Canvas<br/>Sort Order: 100]
        TC[Tooltip Canvas<br/>Sort Order: 200]
        DC[Debug Canvas<br/>Sort Order: 999]
    end
    
    subgraph "UI管理层"
        UM[UIManager<br/>Singleton]
        PF[PanelFactory]
        TM[TransitionManager]
        IM[InputManager]
    end
    
    subgraph "面板系统"
        BP[UIPanel Base Class]
        MMP[MainMenuPanel]
        GHP[GameHUDPanel]
        CDP[CardDetailPanel]
        IP[InventoryPanel]
        SP[SettingsPanel]
        DP[DialogPanel]
    end
    
    subgraph "组件系统"
        AD[AttributeDisplay]
        CS[CardSlot]
        PR[ProgressRing]
        NT[NotificationToast]
        RC[ResourceCounter]
        SB[SmartButton]
        ALT[AutoLocalizedText]
    end
    
    UM --> MC
    UM --> PC
    UM --> TC
    UM --> DC
    
    UM --> PF
    UM --> TM
    UM --> IM
    
    PF --> BP
    BP --> MMP
    BP --> GHP
    BP --> CDP
    BP --> IP
    BP --> SP
    BP --> DP
    
    MMP --> SB
    GHP --> AD
    GHP --> RC
    CDP --> PR
    IP --> CS
    SP --> ALT
    DP --> NT
    
    style UM fill:#e3f2fd
    style BP fill:#f3e5f5
    style MC fill:#fff3e0
    style PC fill:#fff3e0
        
```

## 2. 面板生命周期图
```mermaid

stateDiagram-v2
    [*] --> Created: Instantiate
    Created --> Initialized: Awake()
    Initialized --> Ready: Start()
    Ready --> Showing: ShowPanel()
    Showing --> Visible: OnPanelShow()
    Visible --> Hiding: HidePanel()
    Hiding --> Hidden: OnPanelHide()
    Hidden --> Showing: ShowPanel()
    Hidden --> Destroyed: Destroy
    Destroyed --> [*]
    
    Visible --> Updated: OnPanelUpdate()
    Updated --> Visible
    
    note right of Showing
        播放入场动画
        设置输入焦点
        触发显示事件
    end note
    
    note right of Hiding
        播放退场动画
        清理资源
        触发隐藏事件
    end note
        
```

## 3. 输入处理流程图
```mermaid

graph TD
    A[用户输入] --> B{输入类型}
    
    B -->|鼠标| C[Mouse Input]
    B -->|触摸| D[Touch Input]
    B -->|键盘| E[Keyboard Input]
    B -->|手柄| F[Gamepad Input]
    
    C --> G[EventSystem]
    D --> G
    E --> G
    F --> G
    
    G --> H[GraphicRaycaster]
    H --> I{命中UI元素?}
    
    I -->|是| J[UI Element]
    I -->|否| K[Background]
    
    J --> L[Input Validation]
    L --> M{验证通过?}
    
    M -->|是| N[Execute Action]
    M -->|否| O[Show Error Feedback]
    
    N --> P[Update UI State]
    P --> Q[Trigger Events]
    Q --> R[Visual Feedback]
    
    O --> S[Error Animation]
    S --> T[Audio Feedback]
    
    K --> U[Global Input Handler]
    U --> V[Camera Control]
    
    style A fill:#e1f5fe
    style G fill:#fff3e0
    style N fill:#e8f5e8
    style O fill:#ffebee
        
```

## 4. 动画系统架构图
```mermaid

graph TB
    subgraph "动画框架"
        UA[Unity Animation]
        DT[DOTween]
        CAM[Custom Animation Manager]
    end
    
    subgraph "动画类型"
        EA[入场动画]
        EXA[退场动画]
        IA[交互动画]
        FA[反馈动画]
    end
    
    subgraph "入场动画"
        FI[淡入 Fade In]
        SI[滑入 Slide In]
        SCI[缩放 Scale In]
        BI[弹跳 Bounce In]
    end
    
    subgraph "退场动画"
        FO[淡出 Fade Out]
        SO[滑出 Slide Out]
        SCO[缩放 Scale Out]
    end
    
    subgraph "交互动画"
        BP[按钮按下]
        BH[按钮悬停]
        CS[卡牌选择]
        DF[拖拽反馈]
    end
    
    subgraph "反馈动画"
        SF[成功闪烁]
        ES[错误摇晃]
        VC[数值变化]
    end
    
    subgraph "性能优化"
        AP[动画池化]
        AB[动画批处理]
        AC[动画裁剪]
        AFA[帧率适配]
    end
    
    CAM --> UA
    CAM --> DT
    
    CAM --> EA
    CAM --> EXA
    CAM --> IA
    CAM --> FA
    
    EA --> FI
    EA --> SI
    EA --> SCI
    EA --> BI
    
    EXA --> FO
    EXA --> SO
    EXA --> SCO
    
    IA --> BP
    IA --> BH
    IA --> CS
    IA --> DF
    
    FA --> SF
    FA --> ES
    FA --> VC
    
    CAM --> AP
    CAM --> AB
    CAM --> AC
    CAM --> AFA
    
    style CAM fill:#e3f2fd
    style EA fill:#e8f5e8
    style EXA fill:#fff3e0
    style IA fill:#f3e5f5
    style FA fill:#ffebee
        
```

## 5. 本地化流程图
```mermaid

graph TD
    A[游戏启动] --> B[检测系统语言]
    B --> C[加载语言配置]
    C --> D[初始化本地化管理器]
    
    D --> E[加载文本资源]
    E --> F[加载字体资源]
    F --> G[设置UI文本]
    
    H[用户切换语言] --> I[保存语言设置]
    I --> J[卸载当前资源]
    J --> K[加载新语言资源]
    K --> L[更新所有UI文本]
    L --> M[调整布局]
    M --> N[切换字体]
    N --> O[完成切换]
    
    P[文本本地化] --> Q{是否需要参数?}
    Q -->|是| R[参数替换]
    Q -->|否| S[直接返回文本]
    R --> T[格式化文本]
    T --> U[返回本地化文本]
    S --> U
    
    V[RTL语言处理] --> W[检测文本方向]
    W --> X[调整布局方向]
    X --> Y[翻转UI元素]
    Y --> Z[应用RTL样式]
    
    style A fill:#e1f5fe
    style H fill:#fff3e0
    style P fill:#e8f5e8
    style V fill:#f3e5f5
        
```

## 6. 性能优化图
```mermaid

graph LR
    subgraph "渲染性能"
        DC[Draw Calls]
        UB[UI Batching]
        CO[Canvas Optimization]
        OR[Overdraw Reduction]
    end
    
    subgraph "内存优化"
        TM[Texture Memory]
        MM[Mesh Memory]
        SM[Script Memory]
        OP[Object Pooling]
    end
    
    subgraph "CPU优化"
        LC[Layout Calculations]
        AP[Animation Performance]
        EP[Event Processing]
        BC[Batch Updates]
    end
    
    subgraph "优化策略"
        UIAtlas[UI Atlas]
        VirtualScrolling[Virtual Scrolling]
        LazyLoading[Lazy Loading]
        LOD[Level of Detail]
    end
    
    subgraph "性能监控"
        FPS[Frame Rate]
        Memory[Memory Usage]
        DrawCalls[Draw Call Count]
        Profiler[Unity Profiler]
    end
    
    DC --> UB
    UB --> UIAtlas
    CO --> VirtualScrolling
    OR --> LazyLoading
    
    TM --> OP
    MM --> OP
    SM --> OP
    OP --> LOD
    
    LC --> BC
    AP --> BC
    EP --> BC
    BC --> FPS
    
    FPS --> Profiler
    Memory --> Profiler
    DrawCalls --> Profiler
    
    style UIAtlas fill:#e8f5e8
    style VirtualScrolling fill:#e8f5e8
    style LazyLoading fill:#e8f5e8
    style LOD fill:#e8f5e8
    style Profiler fill:#e3f2fd
        
```

## 图表说明

### 颜色编码
- 🔵 蓝色: 核心管理器和系统
- 🟠 橙色: 用户交互和输入
- 🟢 绿色: 正常流程和优化方案
- 🟣 紫色: UI组件和面板
- 🔴 红色: 错误处理和性能问题

### 架构层次
1. **管理层**: UIManager, PanelFactory, TransitionManager
2. **面板层**: 各种UI面板和对话框
3. **组件层**: 自定义UI组件
4. **框架层**: Unity UGUI和第三方库

### 关键流程
- **面板管理**: 创建 → 显示 → 更新 → 隐藏 → 销毁
- **输入处理**: 输入 → 验证 → 执行 → 反馈
- **动画系统**: 触发 → 播放 → 完成 → 回调
- **本地化**: 检测 → 加载 → 应用 → 更新

### 性能优化重点
- UI批处理减少绘制调用
- 对象池化减少内存分配
- 虚拟滚动优化大列表
- 分帧处理避免卡顿
