# <PERSON>'s Game UI系统架构文档

## 概述
Sultan's Game采用Unity UGUI框架构建，实现了完整的现代UI系统，支持多平台、多语言和丰富的交互体验。

## 核心架构

### 1. Canvas层次结构
```
Main Canvas (Sort Order: 0)
├── Game HUD Panel
├── Card Hand Area
└── Table Area

Popup Canvas (Sort Order: 100)
├── Settings Panel
├── Inventory Panel
└── Dialog Panel

Tooltip Canvas (Sort Order: 200)
├── Card Detail Tooltip
└── Help Tooltips

Debug Canvas (Sort Order: 999)
└── Debug Information (Development only)
```

### 2. UI管理器系统
```csharp
public class UIManager : MonoBehaviour, Singleton<UIManager>
{
    // 面板管理
    public T ShowPanel<T>(PanelData data = null) where T : UIPanel;
    public void HidePanel<T>() where T : UIPanel;
    public T GetPanel<T>() where T : UIPanel;

    // 焦点管理
    public void SetInputFocus(UIPanel panel);

    // 资源管理
    private Dictionary<Type, UIPanel> panelCache;
    private UIPanelFactory panelFactory;
}
```

### 3. 面板基类设计
```csharp
public abstract class UIPanel : MonoBehaviour
{
    [SerializeField] protected PanelType panelType;
    [SerializeField] protected bool isModal;
    [SerializeField] protected AnimationClip showAnimation;
    [SerializeField] protected AnimationClip hideAnimation;

    // 生命周期方法
    public abstract void OnPanelShow(PanelData data);
    public abstract void OnPanelHide();
    public abstract void OnPanelUpdate();
    public abstract PanelType GetPanelType();
}
```

## 核心组件

### 1. 自定义UI组件
- **AttributeDisplay**: 属性值显示组件
- **CardSlot**: 卡牌放置槽组件
- **ProgressRing**: 环形进度条组件
- **NotificationToast**: 通知提示组件
- **ResourceCounter**: 资源计数器组件

### 2. 增强Unity组件
- **SmartButton**: 增强按钮（音效、触觉反馈、冷却）
- **AutoLocalizedText**: 自动本地化文本
- **ResponsiveImage**: 响应式图片
- **SmartScrollView**: 智能滚动视图

### 3. 布局系统
- **响应式设计**: 支持多种屏幕尺寸和分辨率
- **安全区域处理**: 自动适配刘海屏和导航栏
- **动态布局**: 卡牌手牌弧形布局、桌面网格布局

## 动画系统

### 1. 动画框架
- **Unity Animation**: 复杂状态机动画
- **DOTween集成**: 简单属性动画
- **自定义动画管理器**: 队列和中断处理

### 2. 动画类型
- **入场动画**: 淡入、滑入、缩放、弹跳
- **退场动画**: 淡出、滑出、缩放
- **交互动画**: 按钮按下、悬停、选择
- **反馈动画**: 成功闪烁、错误摇晃、数值变化

### 3. 性能优化
- **动画池化**: 重用动画对象
- **批处理**: 组合相似动画
- **裁剪**: 跳过屏幕外动画
- **帧率适配**: 基于性能调整质量

## 输入处理

### 1. 多平台输入支持
- **鼠标输入**: 点击、拖拽、悬停、滚轮
- **触摸输入**: 点击、长按、拖拽、手势
- **键盘输入**: 导航、快捷键、文本输入
- **手柄输入**: 按钮映射、摇杆导航

### 2. 输入管理器
```csharp
public class UIInputManager : MonoBehaviour
{
    // 输入路由
    public void RouteInputEvent(InputEvent inputEvent);

    // 焦点管理
    public void SetFocus(Selectable target);
    public Selectable GetCurrentFocus();

    // 输入验证
    public bool ValidateInput(InputData data);
}
```

### 3. 无障碍功能
- **屏幕阅读器支持**: 文本转语音
- **视觉无障碍**: 高对比度、色盲友好
- **运动无障碍**: 可调触摸目标、单手操作

## 本地化系统

### 1. 多语言支持
支持10种语言：英语、中文（简/繁）、日语、韩语、西班牙语、法语、德语、俄语、阿拉伯语

### 2. 本地化管理器
```csharp
public class LocalizationManager : MonoBehaviour, Singleton<LocalizationManager>
{
    // 语言切换
    public void SetLanguage(SystemLanguage language);

    // 文本获取
    public string GetLocalizedText(string key, params object[] args);

    // 字体切换
    public Font GetLocalizedFont(SystemLanguage language);
}
```

### 3. 布局适配
- **文本扩展处理**: 自动调整布局适应不同语言
- **RTL语言支持**: 阿拉伯语等从右到左语言
- **文化适配**: 颜色、数字、日期格式本地化

## 性能优化

### 1. 渲染优化
- **UI批处理**: 减少绘制调用
- **Canvas分层**: 避免全画布重建
- **过度绘制减少**: 优化透明度和重叠

### 2. 内存优化
- **对象池**: UI元素重用
- **纹理压缩**: 减少内存占用
- **动态加载**: 按需加载UI资源

### 3. CPU优化
- **布局缓存**: 避免重复计算
- **事件池化**: 减少GC分配
- **分帧处理**: 大量操作分帧执行

## 开发最佳实践

### 1. 架构设计
- 使用MVVM模式分离UI和逻辑
- 实现完整的事件驱动架构
- 保持组件的单一职责原则

### 2. 性能考虑
- 使用对象池管理频繁创建的UI
- 避免在Update中进行昂贵操作
- 合理使用Canvas和Layout组件

### 3. 用户体验
- 提供清晰的视觉反馈
- 实现流畅的动画过渡
- 支持多种输入方式

### 4. 可维护性
- 编写清晰的组件接口
- 实现完整的单元测试
- 使用一致的命名规范

## 扩展指南

### 1. 添加新面板
1. 继承UIPanel基类
2. 实现必要的生命周期方法
3. 在UIManager中注册面板类型
4. 创建对应的预制体

### 2. 自定义UI组件
1. 继承适当的Unity UI组件
2. 实现自定义功能和属性
3. 添加编辑器支持（可选）
4. 编写使用文档和示例

### 3. 新动画类型
1. 在动画管理器中定义新类型
2. 实现动画逻辑和参数
3. 添加性能优化措施
4. 更新动画编辑工具

## 故障排除

### 常见问题
1. **UI元素不响应**: 检查Raycast Target设置
2. **布局错乱**: 验证Layout组件配置
3. **动画卡顿**: 分析性能瓶颈和优化
4. **本地化问题**: 检查字体和文本配置

### 调试工具
- Unity Frame Debugger: 分析渲染性能
- UI Profiler: 监控UI性能
- Layout Debugger: 调试布局问题
- Accessibility Checker: 验证无障碍功能
