# -*- coding: utf-8 -*-
# IDA Pro Multi-layer Password Hunter Script
# Compatible version for IDA Pro

import ida_bytes
import ida_name
import ida_search
import ida_funcs
import ida_xref
import idc
import idaapi
import idautils

class MultiLayerPasswordHunter:
    def __init__(self):
        self.found_passwords = ["Sultan"]
        self.potential_passwords = []
        self.decryption_functions = []
        
    def hunt_layer2_passwords(self):
        print("Searching for layer 2 passwords...")
        
        self.search_around_known_password()
        self.search_in_decrypt_functions()
        self.search_password_generation()
        self.search_config_passwords()
    
    def search_around_known_password(self):
        print("Searching around 'Sultan' password...")

        sultan_addr = ida_search.find_text(0, 0, 0, "Sultan", ida_search.SEARCH_DOWN)
        found_addresses = []

        while sultan_addr != idaapi.BADADDR and len(found_addresses) < 10:
            if sultan_addr not in found_addresses:
                print("Found 'Sultan' at address: 0x%X" % sultan_addr)
                found_addresses.append(sultan_addr)
                self.analyze_surrounding_code(sultan_addr)

            # Move past the current string to avoid infinite loop
            next_addr = sultan_addr + len("Sultan")
            sultan_addr = ida_search.find_text(next_addr, 0, 0, "Sultan", ida_search.SEARCH_DOWN)
    
    def analyze_surrounding_code(self, addr):
        print("Analyzing code around address: 0x%X" % addr)
        
        func = ida_funcs.get_func(addr)
        if func:
            func_name = ida_name.get_name(func.start_ea)
            print("Function: %s at 0x%X" % (func_name, func.start_ea))
            
            self.search_strings_in_function(func)
            self.analyze_function_xrefs(func.start_ea)
    
    def search_strings_in_function(self, func):
        current_addr = func.start_ea
        
        while current_addr < func.end_ea:
            if ida_bytes.is_strlit(ida_bytes.get_flags(current_addr)):
                str_content = ida_bytes.get_strlit_contents(current_addr, -1, ida_bytes.STRTYPE_C)
                if str_content and len(str_content) > 3:
                    try:
                        decoded_str = str_content.decode('utf-8', errors='ignore')
                        print("String in function: '%s'" % decoded_str)
                        
                        if self.is_potential_password(decoded_str):
                            self.potential_passwords.append({
                                'password': decoded_str,
                                'address': current_addr,
                                'context': 'function_string'
                            })
                            print("*** POTENTIAL PASSWORD: '%s' ***" % decoded_str)
                    except:
                        pass
            
            current_addr = ida_bytes.next_head(current_addr, func.end_ea)
    
    def search_in_decrypt_functions(self):
        print("Searching for decryption functions...")
        
        decrypt_patterns = [
            "decrypt", "Decrypt", "DECRYPT",
            "decode", "Decode", "DECODE", 
            "unlock", "Unlock", "UNLOCK",
            "decipher", "Decipher",
            "unpack", "Unpack",
            "crypt", "Crypt", "CRYPT"
        ]
        
        for pattern in decrypt_patterns:
            addr = ida_search.find_text(0, 0, 0, pattern, ida_search.SEARCH_DOWN)
            
            while addr != idaapi.BADADDR:
                func = ida_funcs.get_func(addr)
                if func and func.start_ea not in [f['addr'] for f in self.decryption_functions]:
                    func_name = ida_name.get_name(func.start_ea)
                    print("Found decrypt function: %s at 0x%X" % (func_name, func.start_ea))
                    
                    self.decryption_functions.append({
                        'name': func_name,
                        'addr': func.start_ea,
                        'pattern': pattern
                    })
                    
                    self.analyze_decrypt_function(func)
                
                addr = ida_search.find_text(addr + 1, 0, 0, pattern, ida_search.SEARCH_DOWN)
    
    def analyze_decrypt_function(self, func):
        print("Analyzing decrypt function: 0x%X" % func.start_ea)
        
        current_addr = func.start_ea
        
        while current_addr < func.end_ea:
            insn = idaapi.insn_t()
            if idaapi.decode_insn(insn, current_addr):
                for i in range(insn.ops.size()):
                    op = insn.ops[i]
                    if op.type == idaapi.o_imm:
                        value = op.value
                        if 0x20 <= value <= 0x7E:
                            print("Possible key byte: 0x%02X ('%s')" % (value, chr(value)))
            
            current_addr = ida_bytes.next_head(current_addr, func.end_ea)
    
    def search_password_generation(self):
        print("Searching for password generation functions...")
        
        generation_patterns = [
            "generate", "Generate", "GENERATE",
            "create", "Create", "CREATE",
            "build", "Build", "BUILD",
            "make", "Make", "MAKE"
        ]
        
        for pattern in generation_patterns:
            combined_patterns = [
                pattern + "Password",
                pattern + "Key", 
                pattern + "_password",
                pattern + "_key"
            ]
            
            for combined in combined_patterns:
                addr = ida_search.find_text(0, 0, 0, combined, ida_search.SEARCH_DOWN)
                if addr != idaapi.BADADDR:
                    print("Found password generation function: %s at 0x%X" % (combined, addr))
                    
                    func = ida_funcs.get_func(addr)
                    if func:
                        self.analyze_password_generation_function(func)
    
    def analyze_password_generation_function(self, func):
        print("Analyzing password generation function: 0x%X" % func.start_ea)
        print("*** SUGGESTION: Set breakpoint at 0x%X for dynamic analysis ***" % func.start_ea)
        
        self.search_strings_in_function(func)
    
    def search_config_passwords(self):
        print("Searching for config passwords...")
        
        config_patterns = [
            "config", "Config", "CONFIG",
            "setting", "Setting", "SETTING",
            "option", "Option", "OPTION"
        ]
        
        for pattern in config_patterns:
            addr = ida_search.find_text(0, 0, 0, pattern, ida_search.SEARCH_DOWN)
            
            while addr != idaapi.BADADDR:
                print("Found config related: %s at 0x%X" % (pattern, addr))
                
                self.analyze_surrounding_strings(addr)
                
                addr = ida_search.find_text(addr + 1, 0, 0, pattern, ida_search.SEARCH_DOWN)
    
    def analyze_surrounding_strings(self, addr):
        for offset in range(-0x100, 0x100, 4):
            check_addr = addr + offset
            if ida_bytes.is_strlit(ida_bytes.get_flags(check_addr)):
                str_content = ida_bytes.get_strlit_contents(check_addr, -1, ida_bytes.STRTYPE_C)
                if str_content:
                    try:
                        decoded_str = str_content.decode('utf-8', errors='ignore')
                        if self.is_potential_password(decoded_str):
                            print("*** POTENTIAL PASSWORD NEARBY: '%s' at 0x%X ***" % (decoded_str, check_addr))
                            self.potential_passwords.append({
                                'password': decoded_str,
                                'address': check_addr,
                                'context': 'config_nearby'
                            })
                    except:
                        pass
    
    def is_potential_password(self, text):
        if len(text) < 4 or len(text) > 50:
            return False
        
        exclude_patterns = [
            'function', 'class', 'method', 'return', 'void',
            'string', 'int', 'bool', 'float', 'double',
            'public', 'private', 'static', 'const'
        ]
        
        text_lower = text.lower()
        if any(pattern in text_lower for pattern in exclude_patterns):
            return False
        
        password_keywords = [
            'pass', 'key', 'secret', 'token', 'auth',
            'crypt', 'hash', 'salt', 'seed', 'sultan'
        ]
        
        if any(keyword in text_lower for keyword in password_keywords):
            return True
        
        has_alpha = any(c.isalpha() for c in text)
        has_digit = any(c.isdigit() for c in text)
        
        return has_alpha and (has_digit or len(text) >= 6)
    
    def analyze_function_xrefs(self, func_addr):
        print("Analyzing function xrefs: 0x%X" % func_addr)
        
        for xref in idautils.XrefsTo(func_addr):
            caller_func = ida_funcs.get_func(xref.frm)
            if caller_func:
                caller_name = ida_name.get_name(caller_func.start_ea)
                print("Called from: %s at 0x%X" % (caller_name, xref.frm))
                
                self.search_strings_in_function(caller_func)
    
    def generate_report(self):
        print("\n" + "="*60)
        print("MULTI-LAYER PASSWORD SEARCH REPORT")
        print("="*60)
        
        print("\nKnown passwords: %d" % len(self.found_passwords))
        for pwd in self.found_passwords:
            print("   * %s" % pwd)
        
        print("\nPotential passwords: %d" % len(self.potential_passwords))
        for pwd_info in self.potential_passwords:
            print("   * '%s' at 0x%X (%s)" % (pwd_info['password'], pwd_info['address'], pwd_info['context']))
        
        print("\nDecryption functions: %d" % len(self.decryption_functions))
        for func_info in self.decryption_functions:
            print("   * %s at 0x%X" % (func_info['name'], func_info['addr']))
        
        print("\nSUGGESTED NEXT STEPS:")
        print("   1. Set breakpoints at decryption functions")
        print("   2. Test potential passwords for decryption")
        print("   3. Analyze password generation algorithms")
        print("   4. Check for third layer encryption")
        
        if self.potential_passwords:
            print("\n*** IMPORTANT: Found %d potential passwords! ***" % len(self.potential_passwords))
            print("*** Try these passwords in your decryption attempts ***")

print("Starting Sultan's Game multi-layer password search...")

hunter = MultiLayerPasswordHunter()
hunter.hunt_layer2_passwords()
hunter.generate_report()

print("\nMulti-layer password search completed!")
