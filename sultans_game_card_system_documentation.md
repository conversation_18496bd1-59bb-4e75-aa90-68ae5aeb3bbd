# Sultan's Game 卡牌系统架构文档

## 概述
Sultan's Game的卡牌系统是游戏的核心机制，采用属性驱动的设计，支持丰富的交互、效果和收集玩法。

## 核心架构

### 1. 卡牌数据模型
```csharp
public class Card : MonoBehaviour
{
    // 基础信息
    [SerializeField] private int _id;           // 配置ID
    [SerializeField] private int _uid;          // 运行时唯一ID
    [SerializeField] private string _name;      // 卡牌名称
    [SerializeField] private string _title;     // 副标题
    [SerializeField] private string _description; // 描述
    [SerializeField] private string _type;      // 类型
    [SerializeField] private int _rare;         // 稀有度
    [SerializeField] private string _icon;      // 图标路径

    // 五维属性
    [SerializeField] private int _physique;     // 体魄
    [SerializeField] private int _charm;        // 魅力
    [SerializeField] private int _intelligence; // 智慧
    [SerializeField] private int _fortune;      // 财富
    [SerializeField] private int _spirit;       // 精神

    // 状态字段
    [SerializeField] private bool _isEquipped;  // 是否装备
    [SerializeField] private bool _isLocked;    // 是否锁定
    [SerializeField] private bool _isUsed;      // 是否已使用
    [SerializeField] private bool _isSelected;  // 是否选中

    // 扩展字段
    [SerializeField] private List<string> _tags; // 标签系统
}
```

### 2. 卡牌管理系统
- **GameController**: 主要的卡牌生命周期管理器
- **CardController**: 单个卡牌的UI控制器
- **Player集合**: hand、table、deck三个主要集合

### 3. 交互系统
- **点击选择**: 单击选择，双击快速装备
- **拖拽操作**: 支持手牌到桌面的拖拽
- **悬停效果**: 显示详细信息和工具提示
- **验证系统**: 实时检查操作有效性

## 关键特性

### 属性系统
五维属性设计：
- 🔴 **体魄**: 影响体力相关仪式
- 💗 **魅力**: 影响社交相关仪式
- 🔵 **智慧**: 影响学习相关仪式
- 🟡 **财富**: 影响经济相关仪式
- 🟣 **精神**: 影响精神相关仪式

### 效果系统
- **被动效果**: 装备时持续提供属性加成
- **主动效果**: 使用时触发的一次性效果
- **协同效果**: 卡牌组合产生的额外效果
- **动态效果**: 基于游戏状态的条件效果

### 收集系统
- **多种获取方式**: 故事奖励、随机遭遇、交易、合成
- **进化系统**: 卡牌随使用而成长
- **熟练度系统**: 玩家对卡牌类型的掌握度
- **成就系统**: 收集特定组合的奖励

## 性能考虑

### 内存使用
- 单张卡牌: ~200-300 bytes
- 活跃卡牌集合: ~20-35 KB
- UI对象: ~5-10 MB
- 优化策略: 对象池、延迟加载、纹理压缩

### CPU性能
- 卡牌创建: O(1), ~100-200 cycles
- 选择操作: O(n), ~50-100 cycles per card
- 效果计算: O(n*m), ~200-500 cycles
- 目标: < 16.67ms per frame (60 FPS)

## 扩展性设计

### 新卡牌类型
1. 创建新的CardConfig配置
2. 添加相应的类型标识
3. 实现特定的效果逻辑
4. 更新UI显示逻辑

### 新属性维度
1. 扩展Card类的属性字段
2. 更新UI显示组件
3. 调整仪式计算逻辑
4. 添加相应的视觉主题

### 新交互方式
1. 扩展CardController的输入处理
2. 添加新的验证规则
3. 实现相应的视觉反馈
4. 更新事件系统

## 最佳实践

### 开发指南
- 保持数据模型的纯净性
- 使用事件系统解耦组件
- 实现完整的验证机制
- 提供丰富的视觉反馈

### 性能优化
- 使用对象池管理UI对象
- 批量处理集合操作
- 缓存计算结果
- 异步加载资源

### 用户体验
- 提供清晰的操作反馈
- 实现智能的推荐系统
- 支持个性化定制
- 确保操作的一致性

## 故障排除

### 常见问题
1. **卡牌重复**: 检查UID生成逻辑
2. **UI不同步**: 确认事件订阅正确
3. **性能问题**: 分析对象创建和销毁
4. **交互失效**: 验证输入处理链

### 调试工具
- 卡牌状态检查器
- 性能分析器
- 事件追踪器
- 内存使用监控

## 未来发展

### 计划功能
- AI驱动的卡牌推荐
- 动态内容生成
- 多人协作模式
- 云端同步系统

### 技术升级
- 数据驱动的配置系统
- 模块化的效果系统
- 可视化的编辑工具
- 自动化的测试框架
