# Sultan's Game 事件系统可视化图表

## 1. 卡牌交互事件流程
```mermaid

graph TD
    A[用户点击卡牌] --> B[UI检测点击事件]
    B --> C[发布CardEventArgs Selected]
    C --> D[GameController.OnCardEvent]
    C --> E[UIManager.UpdateSelection]
    D --> F[更新CurrentSelect]
    E --> G[高亮选中卡牌]
    F --> H[检查仪式条件]
    G --> I[显示卡牌详情]
    H --> J[显示可用仪式]
    
    K[用户开始拖拽] --> L[OnBeginDrag事件]
    L --> M[发布CardEventArgs Dragged]
    M --> N[GameController.DragCard]
    M --> O[CardController.SetDragging]
    N --> P[设置CurrentDrag]
    O --> Q[移动到拖拽层]
    P --> R[检查放置目标]
    Q --> S[显示拖拽效果]
    
    T[用户释放卡牌] --> U[OnEndDrag事件]
    U --> V[发布CardEventArgs Dropped]
    V --> W[GameController.HandleDrop]
    V --> X[RiteSystem.CheckSatisfaction]
    W --> Y{是否有效放置?}
    Y -->|是| Z[执行仪式]
    Y -->|否| AA[返回原位置]
    Z --> BB[发布RiteEventArgs Started]
    
    style A fill:#e1f5fe
    style C fill:#fff3e0
    style M fill:#fff3e0
    style V fill:#fff3e0
    style BB fill:#e8f5e8
    
```

## 2. 仪式执行流程
```mermaid

graph TD
    A[仪式条件满足] --> B[发布RiteEventArgs Started]
    B --> C[RiteManager.StartRite]
    B --> D[UIManager.ShowRiteAnimation]
    C --> E[锁定游戏输入]
    D --> F[播放仪式动画]
    E --> G[计算仪式效果]
    F --> H[播放音效]
    
    G --> I[应用属性变化]
    I --> J[发布PlayerEventArgs StatsChanged]
    J --> K[Player.UpdateStats]
    J --> L[UIManager.UpdatePlayerUI]
    K --> M[更新玩家数据]
    L --> N[刷新UI显示]
    
    H --> O[动画播放完成]
    O --> P[发布RiteEventArgs Completed]
    P --> Q[RiteManager.CompleteRite]
    P --> R[GameController.CheckGameOver]
    Q --> S[解锁游戏输入]
    R --> T{游戏是否结束?}
    T -->|是| U[发布GameStateEventArgs GameOver]
    T -->|否| V[继续游戏]
    U --> W[显示游戏结束界面]
    
    style A fill:#e1f5fe
    style B fill:#fff3e0
    style J fill:#fff3e0
    style P fill:#fff3e0
    style U fill:#ffebee
    
```

## 3. 事件系统架构
```mermaid

graph TB
    subgraph "事件系统核心"
        ES[GameEventSystem]
        ES --> ED[eventListeners Dictionary]
        ES --> SUB[Subscribe Method]
        ES --> PUB[Publish Method]
        ES --> UNSUB[Unsubscribe Method]
    end
    
    subgraph "事件参数类层次"
        GEA[GameEventArgs 基类]
        GEA --> CEA[CardEventArgs]
        GEA --> PEA[PlayerEventArgs]
        GEA --> REA[RiteEventArgs]
        GEA --> UEA[UIEventArgs]
        GEA --> GSEA[GameStateEventArgs]
    end
    
    subgraph "事件发布者"
        GC[GameController]
        CC[CardController]
        RM[RiteManager]
        UM[UIManager]
        PM[Player]
    end
    
    subgraph "事件监听者"
        GC2[GameController]
        UM2[UIManager]
        AM[AudioManager]
        EM[EffectManager]
        SM[SaveManager]
    end
    
    GC --> PUB
    CC --> PUB
    RM --> PUB
    UM --> PUB
    PM --> PUB
    
    SUB --> GC2
    SUB --> UM2
    SUB --> AM
    SUB --> EM
    SUB --> SM
    
    PUB --> CEA
    PUB --> PEA
    PUB --> REA
    PUB --> UEA
    PUB --> GSEA
    
    style ES fill:#e3f2fd
    style GEA fill:#f3e5f5
    style PUB fill:#fff3e0
    
```

## 4. 性能分析图表
```mermaid

graph LR
    subgraph "内存使用分析"
        M1[事件监听器字典<br/>~2-5 KB]
        M2[事件参数对象<br/>24-56 bytes/event]
        M3[GC压力<br/>中等]
    end
    
    subgraph "CPU开销分析"
        C1[订阅操作<br/>O1 - 字典查找]
        C2[发布操作<br/>On - 遍历监听器]
        C3[取消订阅<br/>On - 列表移除]
    end
    
    subgraph "性能瓶颈"
        B1[大量监听器时的发布开销]
        B2[频繁的对象分配]
        B3[List.Remove的线性搜索]
        B4[异常处理开销]
    end
    
    subgraph "优化建议"
        O1[实现对象池]
        O2[使用LinkedList]
        O3[添加事件优先级]
        O4[异步事件处理]
    end
    
    M2 --> B2
    C2 --> B1
    C3 --> B3
    B2 --> O1
    B1 --> O3
    B3 --> O2
    B4 --> O4
    
    style B1 fill:#ffebee
    style B2 fill:#ffebee
    style B3 fill:#ffebee
    style B4 fill:#ffebee
    style O1 fill:#e8f5e8
    style O2 fill:#e8f5e8
    style O3 fill:#e8f5e8
    style O4 fill:#e8f5e8
    
```

## 5. UI事件集成
```mermaid

graph TD
    subgraph "Unity UI事件"
        B1[Button.onClick]
        I1[InputField.onValueChanged]
        T1[Toggle.onValueChanged]
        D1[OnPointerEnter/Exit]
        DR1[OnBeginDrag/OnDrag/OnEndDrag]
    end
    
    subgraph "事件桥接层"
        EB[Event Bridge]
        EB --> UEA1[UIEventArgs ButtonClicked]
        EB --> UEA2[UIEventArgs ValueChanged]
        EB --> CEA1[CardEventArgs Hover]
        EB --> CEA2[CardEventArgs Dragged/Dropped]
    end
    
    subgraph "游戏事件系统"
        GES[GameEventSystem]
        GES --> PUB[Publish]
        GES --> SUB[Subscribe]
    end
    
    subgraph "事件处理器"
        GC[GameController]
        UM[UIManager]
        AM[AudioManager]
    end
    
    B1 --> EB
    I1 --> EB
    T1 --> EB
    D1 --> EB
    DR1 --> EB
    
    UEA1 --> PUB
    UEA2 --> PUB
    CEA1 --> PUB
    CEA2 --> PUB
    
    SUB --> GC
    SUB --> UM
    SUB --> AM
    
    style EB fill:#fff3e0
    style GES fill:#e3f2fd
    
```

## 图表说明

### 颜色编码
- 🔵 蓝色: 用户交互/输入
- 🟠 橙色: 事件发布
- 🟢 绿色: 正常流程/优化建议
- 🔴 红色: 错误/性能瓶颈
- 🟣 紫色: 核心系统组件

### 使用说明
这些图表可以在支持Mermaid的Markdown查看器中显示，如：
- GitHub
- GitLab
- Typora
- VS Code (with Mermaid extension)
- 在线Mermaid编辑器

### 交互式查看
建议使用在线Mermaid编辑器 (https://mermaid.live) 查看和编辑这些图表。
