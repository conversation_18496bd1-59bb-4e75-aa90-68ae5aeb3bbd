# Sultan's Game 卡牌系统可视化图表

## 1. 卡牌数据模型类图
```mermaid

classDiagram
    class Card {
        -int _id
        -int _uid
        -string _name
        -string _title
        -string _description
        -string _type
        -int _rare
        -string _icon
        -int _physique
        -int _charm
        -int _intelligence
        -int _fortune
        -int _spirit
        -bool _isEquipped
        -bool _isLocked
        -bool _isUsed
        -bool _isSelected
        -List~string~ _tags
        +FromConfig(CardConfig) Card
        +Clone() Card
        +IsValid() bool
        +GetTotalAttributes() int
    }
    
    class CardConfig {
        +int id
        +string name
        +string type
        +int rarity
        +AttributeSet attributes
        +List~string~ tags
        +string iconPath
    }
    
    class AttributeSet {
        +int physique
        +int charm
        +int intelligence
        +int fortune
        +int spirit
        +GetTotal() int
        +GetAverage() float
    }
    
    class CardController {
        -Card cardData
        -Image cardImage
        -Text nameText
        -AttributeDisplay[] attributeDisplays
        +SetCard(Card) void
        +UpdateVisuals() void
        +OnClick() void
        +OnDrag() void
    }
    
    Card --> CardConfig : "created from"
    Card --> AttributeSet : "contains"
    CardController --> Card : "displays"
    
    note for Card "核心卡牌数据模型\n包含所有游戏逻辑数据"
    note for CardController "UI表现层控制器\n处理用户交互"
        
```

## 2. 卡牌管理流程图
```mermaid

graph TD
    A[游戏开始] --> B[加载卡牌配置]
    B --> C[生成初始卡组]
    C --> D[创建手牌UI]
    
    D --> E[玩家交互]
    E --> F{交互类型}
    
    F -->|点击选择| G[SelectCard]
    F -->|拖拽移动| H[DragCard]
    F -->|右键菜单| I[ShowContextMenu]
    
    G --> J[更新选中状态]
    J --> K[显示卡牌详情]
    K --> L[检查仪式条件]
    
    H --> M[验证移动有效性]
    M --> N{是否有效?}
    N -->|是| O[执行移动]
    N -->|否| P[返回原位置]
    
    O --> Q[更新集合状态]
    Q --> R[触发移动事件]
    R --> S[更新UI布局]
    
    P --> T[显示错误反馈]
    
    I --> U[显示操作选项]
    U --> V[执行选中操作]
    
    S --> E
    T --> E
    V --> E
    
    style A fill:#e1f5fe
    style G fill:#fff3e0
    style H fill:#fff3e0
    style O fill:#e8f5e8
    style P fill:#ffebee
        
```

## 3. 卡牌系统架构图
```mermaid

graph TB
    subgraph "数据层"
        CD[Card Data Model]
        CC[CardConfig System]
        CS[Card Collections]
    end
    
    subgraph "管理层"
        GC[GameController]
        CM[CardManager]
        EM[EffectManager]
    end
    
    subgraph "表现层"
        CCT[CardController]
        UI[UI Components]
        AN[Animation System]
    end
    
    subgraph "交互层"
        IH[Input Handler]
        DND[Drag & Drop]
        VS[Validation System]
    end
    
    subgraph "效果层"
        PE[Passive Effects]
        AE[Active Effects]
        SE[Synergy Effects]
    end
    
    CC --> CD
    CD --> CS
    CS --> CM
    CM --> GC
    GC --> CCT
    CCT --> UI
    UI --> AN
    
    IH --> DND
    DND --> VS
    VS --> CM
    
    CM --> EM
    EM --> PE
    EM --> AE
    EM --> SE
    
    PE --> GC
    AE --> GC
    SE --> GC
    
    style CD fill:#e3f2fd
    style GC fill:#fff3e0
    style CCT fill:#f3e5f5
    style EM fill:#e8f5e8
        
```

## 4. 卡牌交互时序图
```mermaid

sequenceDiagram
    participant Player
    participant UI
    participant CardController
    participant GameController
    participant EffectManager
    
    Player->>UI: 点击卡牌
    UI->>CardController: OnPointerClick()
    CardController->>GameController: SelectCard(card)
    GameController->>GameController: 取消之前选择
    GameController->>CardController: 更新选中状态
    CardController->>UI: 显示选中效果
    
    Player->>UI: 开始拖拽
    UI->>CardController: OnBeginDrag()
    CardController->>GameController: StartDrag(card)
    GameController->>UI: 显示拖拽状态
    
    Player->>UI: 拖拽中
    UI->>CardController: OnDrag()
    CardController->>GameController: UpdateDragPosition()
    GameController->>UI: 更新拖拽位置
    GameController->>UI: 高亮有效区域
    
    Player->>UI: 释放卡牌
    UI->>CardController: OnEndDrag()
    CardController->>GameController: EndDrag(targetZone)
    GameController->>GameController: 验证放置有效性
    
    alt 有效放置
        GameController->>GameController: 移动卡牌
        GameController->>EffectManager: 计算新效果
        EffectManager->>GameController: 返回效果结果
        GameController->>UI: 更新UI布局
        GameController->>UI: 播放成功动画
    else 无效放置
        GameController->>CardController: 返回原位置
        CardController->>UI: 播放失败动画
        GameController->>UI: 显示错误提示
    end
        
```

## 5. 卡牌效果系统图
```mermaid

graph TD
    subgraph "效果触发"
        ET1[卡牌装备]
        ET2[卡牌使用]
        ET3[仪式执行]
        ET4[回合开始/结束]
    end
    
    subgraph "效果类型"
        PE[被动效果]
        AE[主动效果]
        SE[协同效果]
        CE[条件效果]
    end
    
    subgraph "效果计算"
        AC[属性计算]
        BC[加成计算]
        SC[协同计算]
        TC[阈值检查]
    end
    
    subgraph "效果应用"
        PA[属性修改]
        SA[状态改变]
        EA[事件触发]
        UA[UI更新]
    end
    
    ET1 --> PE
    ET2 --> AE
    ET3 --> SE
    ET4 --> CE
    
    PE --> AC
    AE --> BC
    SE --> SC
    CE --> TC
    
    AC --> PA
    BC --> SA
    SC --> EA
    TC --> UA
    
    PA --> UA
    SA --> UA
    EA --> UA
    
    style PE fill:#e8f5e8
    style AE fill:#fff3e0
    style SE fill:#e3f2fd
    style CE fill:#f3e5f5
        
```

## 6. 性能分析图
```mermaid

graph LR
    subgraph "内存使用"
        M1[单卡数据<br/>~200-300 bytes]
        M2[UI对象<br/>~1-2 KB]
        M3[纹理资源<br/>~1-4 MB]
        M4[集合管理<br/>~20-35 KB]
    end
    
    subgraph "CPU开销"
        C1[卡牌创建<br/>O1 ~100-200 cycles]
        C2[选择操作<br/>On ~50-100 cycles]
        C3[效果计算<br/>On*m ~200-500 cycles]
        C4[UI更新<br/>On ~10-50 cycles]
    end
    
    subgraph "性能瓶颈"
        B1[大量UI对象创建/销毁]
        B2[频繁的效果重计算]
        B3[复杂的拖拽验证]
        B4[纹理加载延迟]
    end
    
    subgraph "优化策略"
        O1[对象池化]
        O2[效果缓存]
        O3[分帧处理]
        O4[异步加载]
    end
    
    M2 --> B1
    C3 --> B2
    C2 --> B3
    M3 --> B4
    
    B1 --> O1
    B2 --> O2
    B3 --> O3
    B4 --> O4
    
    style B1 fill:#ffebee
    style B2 fill:#ffebee
    style B3 fill:#ffebee
    style B4 fill:#ffebee
    style O1 fill:#e8f5e8
    style O2 fill:#e8f5e8
    style O3 fill:#e8f5e8
    style O4 fill:#e8f5e8
        
```

## 图表说明

### 颜色编码
- 🔵 蓝色: 数据模型和核心系统
- 🟠 橙色: 管理层和控制逻辑
- 🟣 紫色: UI表现层
- 🟢 绿色: 正常流程和优化方案
- 🔴 红色: 错误处理和性能瓶颈

### 架构层次
1. **数据层**: 卡牌数据模型和配置
2. **管理层**: 游戏逻辑和状态管理
3. **表现层**: UI组件和视觉效果
4. **交互层**: 用户输入和验证
5. **效果层**: 游戏效果和计算

### 关键流程
- **卡牌选择**: 点击 → 选中 → 显示详情
- **卡牌移动**: 拖拽 → 验证 → 移动/返回
- **效果计算**: 触发 → 计算 → 应用 → 更新

### 性能优化重点
- 对象池化减少GC压力
- 效果缓存避免重复计算
- 分帧处理大量操作
- 异步加载减少卡顿
