# Sultan's Game 事件系统架构文档

## 概述
Sultan's Game采用基于观察者模式的事件驱动架构，通过类型安全的泛型事件系统实现组件间的松耦合通信。

## 核心组件

### 1. GameEventSystem (静态类)
游戏的核心事件调度器，负责事件的订阅、发布和管理。

```csharp
// 订阅事件
GameEventSystem.Subscribe<CardEventArgs>(OnCardEvent);

// 发布事件
GameEventSystem.Publish(new CardEventArgs(card, CardEventType.Selected));

// 取消订阅
GameEventSystem.Unsubscribe<CardEventArgs>(OnCardEvent);
```

### 2. 事件参数类层次
所有事件参数都继承自`GameEventArgs`基类：

- **CardEventArgs**: 卡牌相关事件
- **PlayerEventArgs**: 玩家状态事件
- **RiteEventArgs**: 仪式系统事件
- **UIEventArgs**: UI交互事件
- **GameStateEventArgs**: 游戏状态变化事件

## 事件流程示例

### 卡牌选择流程
1. 用户点击卡牌 → UI事件
2. 发布`CardEventArgs(Selected)` → 事件系统
3. GameController处理选择逻辑 → 业务逻辑
4. 更新UI显示 → UI响应

### 仪式执行流程
1. 检测仪式条件满足 → 游戏逻辑
2. 发布`RiteEventArgs(Started)` → 事件系统
3. 播放动画和特效 → UI/音效系统
4. 应用仪式效果 → 游戏状态
5. 发布`RiteEventArgs(Completed)` → 完成通知

## 性能考虑

### 内存使用
- 事件监听器字典: ~2-5 KB
- 事件参数对象: 每次发布分配24-56字节
- GC压力: 中等（短生命周期对象）

### CPU开销
- 订阅: O(1) - 字典查找
- 发布: O(n) - 遍历监听器列表
- 取消订阅: O(n) - 列表移除操作

## 最佳实践

### 1. 事件命名
- 使用描述性的事件类型枚举
- 事件参数类以`EventArgs`结尾
- 方法名使用`On`前缀

### 2. 内存管理
- 及时取消不需要的事件订阅
- 考虑使用对象池优化频繁事件
- 避免在事件处理器中创建大量临时对象

### 3. 错误处理
- 事件处理器中使用try-catch
- 记录异常但不中断其他监听器
- 提供事件处理失败的回退机制

### 4. 调试支持
- 使用事件日志记录关键事件
- 实现事件性能分析工具
- 提供运行时事件状态查看

## 扩展指南

### 添加新事件类型
1. 创建继承自`GameEventArgs`的事件参数类
2. 定义相应的事件类型枚举
3. 在相关组件中发布和监听事件
4. 更新文档和测试用例

### 性能优化
1. 实现事件对象池减少GC压力
2. 添加事件优先级系统
3. 考虑异步事件处理
4. 使用弱引用避免内存泄漏

## 故障排除

### 常见问题
1. **内存泄漏**: 检查是否正确取消事件订阅
2. **性能问题**: 分析事件发布频率和监听器数量
3. **事件丢失**: 确认事件类型匹配和监听器注册
4. **循环依赖**: 避免在事件处理器中发布相同类型事件

### 调试工具
- 事件日志器: 记录所有事件活动
- 性能分析器: 监控事件系统开销
- 状态检查器: 查看当前监听器状态
